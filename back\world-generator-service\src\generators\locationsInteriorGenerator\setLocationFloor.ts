import { TransferLocation, Point, PointWithId } from '../../shared/types/Location';
import { LocationDecorations, TerrainType, MaterialTexture, DecorationZoneType, LocationType } from '../../shared/enums';

// Небольшой, легко расширяемый набор правил для сопоставления декораций с типом поверхности
type DecoRule = {
  decorations: string[]; // ключи декораций для точного совпадения
  matchContains?: string[]; // подстроки названий декораций для поиска
  radius?: number; // радиус применения (чебышёв)
  terrain: TerrainType;
  forRoom?: boolean; // если true, закрасить всю комнату, содержащую декорацию
};

const DEFAULT_RULES: DecoRule[] = [
  { decorations: [LocationDecorations.GASSTATIONPUMP], radius: 0, terrain: TerrainType.ASPHALT },
  { decorations: [LocationDecorations.TOILET], terrain: TerrainType.TILES, forRoom: true },
  // обработка общих названий, содержащих 'asphalt' или 'road'
  { decorations: [], matchContains: ['asphalt', 'road'], radius: 0, terrain: TerrainType.ASPHALT }
];

// Сопоставление текстуры материала с типом поверхности по умолчанию для интерьера
export function materialToTerrain(m?: MaterialTexture): TerrainType {
  switch (m) {
    case MaterialTexture.BRICK:
      return TerrainType.WOOD;
    case MaterialTexture.BETON:
      return TerrainType.BETON;
    case MaterialTexture.WOOD:
      return TerrainType.WOOD;
    case MaterialTexture.METAL:
      return TerrainType.BETON;
    
  }
}

// Вспомогательная функция для генерации ключа по координатам
function key(x: number, y: number) { return `${x},${y}`; }


export function setLocationFloor(location: TransferLocation, rules: DecoRule[] = DEFAULT_RULES): void {
  if (!location) return;

  const size = location.locationSize;
  if (!size || size.length < 2) return;
  const width = size[0];
  const height = size[1];

  // Логика из прежней paintBuildingsBase: определение интерьеров зданий и
  // закраска базовой поверхности/стен/колец. Выполняется в начале для каждой локации,
  // особенно важна для обработки LocationType.OUTDOOR.
  // Гарантировать наличие контейнеров
  if (!location.decorations) location.decorations = {};
  if (!location.floor) {
    location.floor = {} as Record<TerrainType, Point[]>;
    for (const t of Object.values(TerrainType) as TerrainType[]) {
      location.floor[t] = [];
    }
  }

  // Стены/окна/двери считаются препятствиями для определения зданий
  const wallDecorationKeys = [
    LocationDecorations.WALL,
    LocationDecorations.WINDOW,
    LocationDecorations.DOOR,
    LocationDecorations.GARAGEDOOR,
    LocationDecorations.JAILDOOR,
    LocationDecorations.JAILWINDOW,
    LocationDecorations.JAILBARS
  ];
  const walls: Point[] = [];
  for (const wk of wallDecorationKeys) {
    const arr = (location.decorations[wk] || []) as Point[];
    if (arr && arr.length) walls.push(...arr);
  }
  const wallSet = new Set<string>(walls.map(([ax, ay]) => key(ax, ay)));

  // Заливка по периметру для пометки внешней области
  const outside = new Set<string>();
  const queue: Point[] = [];
  for (let xx= -1; xx < width; xx++) {
    for (const yy of [0, height - 1]) {
      if (!wallSet.has(key(xx, yy))) { queue.push([xx, yy]); outside.add(key(xx, yy)); }
    }
  }
  for (let yy= -1; yy < height; yy++) {
    for (const xx of [0, width - 1]) {
      if (!wallSet.has(key(xx, yy))) { queue.push([xx, yy]); outside.add(key(xx, yy)); }
    }
  }

  while (queue.length) {
    const [cx, cy] = queue.shift()!;
    const nbs = [[cx+1,cy],[cx-1,cy],[cx,cy+1],[cx,cy-1]];
    for (const [nx, ny] of nbs) {
      if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
      const k = key(nx, ny);
      if (outside.has(k) || wallSet.has(k)) continue;
      outside.add(k);
      queue.push([nx, ny]);
    }
  }

  // Внутренние тайлы — это не стены и не внешняя область
  const interiorSet = new Set<string>();
  for (let xx= -1; xx < width; xx++) for (let yy= -1; yy < height; yy++) {
    const k = key(xx, yy);
    if (wallSet.has(k)) continue;
    if (!outside.has(k)) interiorSet.add(k);
  }

  // Группировка внутренних областей в компоненты (здания)
  const visited = new Set<string>();
  const buildings: Point[][] = [];
  for (const s of interiorSet) {
    if (visited.has(s)) continue;
    const [sx, sy] = s.split(',').map(n => Number(n)) as [number, number];
    const bqueue: Point[] = [[sx, sy]];
    const comp: Point[] = [];
    visited.add(s);
    while (bqueue.length) {
      const [cx, cy] = bqueue.shift()!;
      comp.push([cx, cy]);
      const nbs = [[cx+1,cy],[cx-1,cy],[cx,cy+1],[cx,cy-1]];
      for (const [nx, ny] of nbs) {
        if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
        const nk = key(nx, ny);
        if (!interiorSet.has(nk) || visited.has(nk)) continue;
        visited.add(nk);
        bqueue.push([nx, ny]);
      }
    }
    buildings.push(comp);
  }
  // отладочные логи удалены

  // Вспомогательная функция для добавления точек в карту location.floor
  function addFloor(t: TerrainType, pts: Point[]) {
  // allow t === 0 (enum may be zero); only skip when pts missing/empty or t is null/undefined
  if (t === undefined || t === null || !pts || !pts.length) return;
    // гарантировать наличие контейнера
    if (!location.floor![t]) location.floor![t] = [];
    // удалить эти точки из других поверхностей, чтобы последняя закраска победила
    const keys = new Set(pts.map(p => key(p[0], p[1])));
    for (const ot of Object.values(TerrainType) as TerrainType[]) {
      if (ot === t) continue;
      const arr = location.floor![ot];
      if (!arr || !arr.length) continue;
      location.floor![ot] = arr.filter(p => !keys.has(key(p[0], p[1])));
    }
    location.floor![t].push(...pts);
  }

  // Закрасить каждый интерьер здания, прилегающие стены и внешнюю окантовку (1 тайл)
  const baseTerrain = materialToTerrain(location.textureMaterial);

  function paintBuildingOutline(building: Point[]) {
    if (!building || !building.length) return;

    // 1) покрасить интерьер здания
    addFloor(baseTerrain, building);

    // 2) найти уникальные стеновые тайлы, которые граничат с интерьером (4-ориентированные)
    const adjacentWalls = new Map<string, Point>();
    for (const [ix, iy] of building) {
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            if (dx === 0 && dy === 0) continue;
            const nx = ix + dx; const ny = iy + dy;
            if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
            const k = key(nx, ny);
            if (wallSet.has(k)) adjacentWalls.set(k, [nx, ny]);
          }
        }
    }
    const wallList = Array.from(adjacentWalls.values());
    if (wallList.length) addFloor(baseTerrain, wallList);

    // 3) собрать внешнюю окантовку — клетки вокруг этих стен, которые не являются стенами и не внутри
    const ring = new Map<string, Point>();
    for (const [wx, wy] of wallList) {
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          const nx = wx + dx; const ny = wy + dy;
          if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
          const nk = key(nx, ny);
          if (wallSet.has(nk)) continue; // не захватывать стены
          if (interiorSet.has(nk)) continue; // не захватывать внутренние клетки
          ring.set(nk, [nx, ny]);
        }
      }
    }
    if (ring.size) addFloor(TerrainType.TILES, Array.from(ring.values()));
  }

  for (const building of buildings) paintBuildingOutline(building);

  // Если зданий нет (interiorSet пуст) — закрашивать только для внутренних/подземных типов.
  // Для OUTDOOR-локаций не закрашивать всю карту как интерьер (чтобы не закрасить всю улицу бетоном).
  if (buildings.length === 0 && location.type !== undefined && location.type !== LocationType.OUTDOOR) {
    // Для закрытых внутренних комнат без явных стен — закрасить все не-стены базовым типом
    const fallback: Point[] = [];
    for (let x = 0; x < width; x++) for (let y = 0; y < height; y++) {
      const k = key(x, y);
      if (!wallSet.has(k)) fallback.push([x, y]);
    }
    addFloor(baseTerrain, fallback);
  }

  // Применить правила, основанные на декорациях (включая правила для комнат)
  // Построить карту декораций: ключ декорации -> точки
  const decorations = location.decorations || {};

  // Вспомогательная функция: найти клетки комнаты через decorationZoneType, если есть
  const roomZones = new Map<number, Point[]>();
  if (location.decorationZoneType) {
    for (const zoneKey of Object.keys(location.decorationZoneType)) {
      const entries = location.decorationZoneType[zoneKey as DecorationZoneType] || [] as PointWithId[];
      for (const [x, y, id] of entries) {
        if (!roomZones.has(id)) roomZones.set(id, []);
        roomZones.get(id)!.push([x, y]);
      }
    }
  }

  // отладочные логи удалены

  // Вспомогательная функция: определить клетки комнаты (включая координаты стен) для заданной точки декорации
  function getRoomPointsForDecoration(px: number, py: number): Point[] {
    // 1) Поиск через roomZones
    for (const [id, pts] of roomZones) {
      if (pts.some(p => p[0] === px && p[1] === py)) {
        const borderWalls: Point[] = [];
        for (const [rx, ry] of pts) {
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              if (dx === 0 && dy === 0) continue;
              const nx = rx + dx; const ny = ry + dy;
              const k = key(nx, ny);
              if (wallSet.has(k)) borderWalls.push([nx, ny]);
            }
          }
        }
        const uniqWalls = Array.from(new Map(borderWalls.map(p => [key(p[0], p[1]), p])).values());
        return pts.concat(uniqWalls);
      }
    }

    // 2) Компоненты зданий
    for (const building of buildings) {
      if (building.some(p => p[0] === px && p[1] === py)) {
        const borderWalls: Point[] = [];
        for (const [ix, iy] of building) {
          for (let dx= -1; dx <= 1; dx++) {
            for (let dy= -1; dy <= 1; dy++) {
              if (dx === 0 && dy === 0) continue;
              const nx = ix + dx; const ny = iy + dy;
              const k = key(nx, ny);
              if (wallSet.has(k)) borderWalls.push([nx, ny]);
            }
          }
        }
        const uniq = Array.from(new Map(borderWalls.map(p => [key(p[0], p[1]), p])).values());
        return building.concat(uniq);
      }
    }

    // 3) Попытка найти прямоугольную комнату, сканируя до ближайших стен в 4 направлениях
    let left= -1, right = -1, top= -1, bottom = -1;
    // сканировать влево
    for (let nx = px; nx >= 0; nx--) {
      if (wallSet.has(key(nx, py))) { left = nx; break; }
    }
    // сканировать вправо
    for (let nx = px; nx < width; nx++) {
      if (wallSet.has(key(nx, py))) { right = nx; break; }
    }
    // сканировать вверх
    for (let ny = py; ny >= 0; ny--) {
      if (wallSet.has(key(px, ny))) { top = ny; break; }
    }
    // сканировать вниз
    for (let ny = py; ny < height; ny++) {
      if (wallSet.has(key(px, ny))) { bottom = ny; break; }
    }

    if (left >= 0 && right >= 0 && top >= 0 && bottom >= 0) {
      // построить прямоугольник, включая стены
      const pts: Point[] = [];
      for (let rx = left; rx <= right; rx++) for (let ry = top; ry <= bottom; ry++) pts.push([rx, ry]);
      return pts;
    }

    // 4) запасной вариант: вернуть один тайл
    return [[px, py]];
  }

  // Применить правила
  for (const rule of rules) {
    // Проверить по точным ключам декораций
    const matchedPoints: Point[] = [];
    for (const decKey of Object.keys(decorations)) {
      const decList = decorations[decKey as any] || [] as Point[];
      const shouldMatchExact = rule.decorations && rule.decorations.length > 0 && rule.decorations.includes(decKey as any);
      const shouldMatchContains = rule.matchContains && rule.matchContains.some(s => decKey.toLowerCase().includes(s));
      if (!shouldMatchExact && !shouldMatchContains) continue;

      for (const [x, y] of decList as Point[]) matchedPoints.push([x, y]);
    }

    // применить точки
    for (const [x, y] of matchedPoints) {
      if (rule.forRoom) {
        const roomPts = getRoomPointsForDecoration(x, y);
        // краткий лог
        try {
          const walls = roomPts.filter(p => wallSet.has(key(p[0], p[1]))).length;
        } catch (e) { }
        addFloor(rule.terrain, roomPts);
        // создать окантовку толщиной 1 тайл вокруг комнаты и покрасить тем же материалом
        const ringMap = new Map<string, Point>();
        const roomSet = new Set(roomPts.map(p => key(p[0], p[1])));
        for (const [ix, iy] of roomPts) {
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              const nx = ix + dx; const ny = iy + dy;
              if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
              const nk = key(nx, ny);
              if (roomSet.has(nk)) continue; // не включать клетки комнаты
              if (interiorSet.has(nk) || wallSet.has(nk)) continue; // пропустить внутренние/стеновые тайлы
              ringMap.set(nk, [nx, ny]);
            }
          }
        }
        if (ringMap.size) addFloor(rule.terrain, Array.from(ringMap.values()));
      } else {
        const r = rule.radius || 0;
        const cells: Point[] = [];
        for (let dx = -r; dx <= r; dx++) {
          for (let dy = -r; dy <= r; dy++) {
            const nx = x + dx; const ny = y + dy;
            if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
            // Радиус по Чебышёву
            if (Math.max(Math.abs(dx), Math.abs(dy)) > r) continue;
            cells.push([nx, ny]);
          }
        }
        addFloor(rule.terrain, cells);
      }
    }
  }

  // Постобработка для городов и деревень: заполнение узких полосок WASTELAND между TILES
  if (location.subtype === 'town' || location.subtype === 'village') {
    // Отладочная информация
    const tilesCount = location.floor[TerrainType.TILES]?.length || 0;
    const wastelandCount = location.floor[TerrainType.WASTELAND]?.length || 0;
    
    fillNarrowWastelandGaps(location, width, height, addFloor);
    
    // Поиск и заполнение замкнутых пустых областей машинами
    fillEnclosedEmptyAreas(location, width, height, addFloor);
    
    const tilesCountAfter = location.floor[TerrainType.TILES]?.length || 0;
    const wastelandCountAfter = location.floor[TerrainType.WASTELAND]?.length || 0;
  }

  // Удалить дубликаты точек в каждом списке поверхности
  for (const t of Object.values(TerrainType) as TerrainType[]) {
    const map = new Map<string, Point>();
    for (const [x, y] of location.floor![t] || []) map.set(key(x, y), [x, y]);
    location.floor![t] = Array.from(map.values());
  }
}

// Функция для заполнения узких пустот между областями TILES (шириной 1-3 тайла)
function fillNarrowWastelandGaps(location: TransferLocation, width: number, height: number, addFloor: (t: TerrainType, pts: Point[]) => void): void {
  // Собираем все точки, которые УЖЕ покрашены в какой-либо тип поверхности
  const paintedSet = new Set<string>();
  const tilesSet = new Set<string>();
  
  for (const terrainType of Object.values(TerrainType) as TerrainType[]) {
    const points = location.floor?.[terrainType] || [];
    for (const [x, y] of points) {
      paintedSet.add(key(x, y));
      if (terrainType === TerrainType.TILES) {
        tilesSet.add(key(x, y));
      }
    }
  }

  // WASTELAND = все точки, которые НЕ покрашены ни в что
  const wastelandSet = new Set<string>();
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const k = key(x, y);
      if (!paintedSet.has(k)) {
        wastelandSet.add(k);
      }
    }
  }


  const pointsToConvertToTiles: Point[] = [];
  const processedPoints = new Set<string>();

  // Ищем пустые области между TILES
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const k = key(x, y);
      if (!wastelandSet.has(k) || processedPoints.has(k)) continue;

      // Проверяем горизонтальный зазор
      let horizontalGap: Point[] = [];
      let tempX = x;
      while (tempX < width && wastelandSet.has(key(tempX, y)) && !processedPoints.has(key(tempX, y))) {
        horizontalGap.push([tempX, y]);
        tempX++;
      }
      
      // Если зазор 1-3 тайла и есть TILES слева и справа
      if (horizontalGap.length >= 1 && horizontalGap.length <= 3) {
        const hasLeftTiles = x > 0 && tilesSet.has(key(x - 1, y));
        const hasRightTiles = tempX < width && tilesSet.has(key(tempX, y));
        
        if (hasLeftTiles && hasRightTiles) {
          pointsToConvertToTiles.push(...horizontalGap);
          for (const [px, py] of horizontalGap) {
            processedPoints.add(key(px, py));
          }
        }
      }

      // Проверяем вертикальный зазор
      let verticalGap: Point[] = [];
      let tempY = y;
      while (tempY < height && wastelandSet.has(key(x, tempY)) && !processedPoints.has(key(x, tempY))) {
        verticalGap.push([x, tempY]);
        tempY++;
      }
      
      // Если зазор 1-3 тайла и есть TILES сверху и снизу
      if (verticalGap.length >= 1 && verticalGap.length <= 3) {
        const hasTopTiles = y > 0 && tilesSet.has(key(x, y - 1));
        const hasBottomTiles = tempY < height && tilesSet.has(key(x, tempY));
        
        if (hasTopTiles && hasBottomTiles) {
          pointsToConvertToTiles.push(...verticalGap);
          for (const [px, py] of verticalGap) {
            processedPoints.add(key(px, py));
          }
        }
      }
    }
  }

  // Конвертируем найденные точки
  if (pointsToConvertToTiles.length > 0) {
    const uniquePoints = Array.from(new Map(pointsToConvertToTiles.map(p => [key(p[0], p[1]), p])).values());
    
    // Убираем декорации с этих клеток
    removeDecorationsFromPoints(location, uniquePoints);
    
    // Добавляем случайные декорации
    addRandomDecorationsToPoints(location, uniquePoints);
    
    // Красим в TILES
    addFloor(TerrainType.TILES, uniquePoints);
  } 
}

// Убираем все декорации с указанных точек
function removeDecorationsFromPoints(location: TransferLocation, points: Point[]): void {
  const pointsToRemove = new Set(points.map(([x, y]) => key(x, y)));
  
  if (!location.decorations) return;
  
  for (const decorationType of Object.keys(location.decorations)) {
    const decorationPoints = location.decorations[decorationType as any] || [];
    location.decorations[decorationType as any] = decorationPoints.filter(
      ([x, y]: Point) => !pointsToRemove.has(key(x, y))
    );
  }
}

// Добавляем случайные декорации на указанные точки
function addRandomDecorationsToPoints(location: TransferLocation, points: Point[]): void {
  const streetDecorations = [
    LocationDecorations.TIRE,
    LocationDecorations.BARREL,
    LocationDecorations.PUDDLE,
    LocationDecorations.LITTER,
    LocationDecorations.TRASHCONTAINER,
    LocationDecorations.UNIVERSALRND,
    LocationDecorations.BOX,
    LocationDecorations.RUIN,
    LocationDecorations.SCELETON,
    LocationDecorations.SAFE,
    LocationDecorations.PALLET
  ];
  
  if (!location.decorations) location.decorations = {};
  
  for (const [x, y] of points) {
    // 30% шанс поставить декорацию
    if (Math.random() < 0.3) {
      const randomDecoration = streetDecorations[Math.floor(Math.random() * streetDecorations.length)];
      
      if (!location.decorations[randomDecoration]) {
        location.decorations[randomDecoration] = [];
      }
      
      location.decorations[randomDecoration].push([x, y]);
    }
  }
}

// Функция для поиска и заполнения замкнутых пустых областей машинами
function fillEnclosedEmptyAreas(location: TransferLocation, width: number, height: number, addFloor: (t: TerrainType, pts: Point[]) => void): void {
  // Собираем все покрашенные точки
  const paintedSet = new Set<string>();
  const tilesSet = new Set<string>();
  
  for (const terrainType of Object.values(TerrainType) as TerrainType[]) {
    const points = location.floor?.[terrainType] || [];
    for (const [x, y] of points) {
      paintedSet.add(key(x, y));
      if (terrainType === TerrainType.TILES) {
        tilesSet.add(key(x, y));
      }
    }
  }

  // Собираем стены для проверки отступов от машин
  const wallSet = new Set<string>();
  if (location.decorations) {
    const wallDecorationKeys = [
      LocationDecorations.WALL,
      LocationDecorations.WINDOW,
      LocationDecorations.DOOR,
      LocationDecorations.GARAGEDOOR,
      LocationDecorations.JAILDOOR,
      LocationDecorations.JAILWINDOW,
      LocationDecorations.JAILBARS
    ];
    for (const wk of wallDecorationKeys) {
      const arr = (location.decorations[wk as any] || []) as Point[];
      if (arr && arr.length) {
        for (const [wx, wy] of arr) {
          wallSet.add(key(wx, wy));
        }
      }
    }
  }

  const visitedSet = new Set<string>();
  const areasToProcess: Point[][] = [];

  // Ищем замкнутые пустые области
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const k = key(x, y);
      if (paintedSet.has(k) || visitedSet.has(k)) continue;

      // Нашли непокрашенную точку, проверяем область
      const area = floodFillArea(x, y, width, height, paintedSet, visitedSet);
      
      if (area.length > 0 && area.length <= 600) { // макс 20x20 = 400 точек
        // Проверяем, что область достаточно замкнута (мягкая проверка)
        if (isAreaMostlyEnclosedByTiles(area, tilesSet, width, height)) {
          areasToProcess.push(area);
        }
      }
    }
  }

  // Обрабатываем найденные области
  for (const area of areasToProcess) {
    // Красим область в TILES с помощью addFloor (правильное управление типами terrain)
    const uniqueAreaPoints = Array.from(new Map(area.map(p => [key(p[0], p[1]), p])).values());
   
    addFloor(TerrainType.TILES, uniqueAreaPoints);

    // Размещаем машины в области
    const carSpots = findCarSpotsInArea(area, wallSet);
    placeCarsInArea(location, carSpots, wallSet, width, height);
  }
}

// Заливка для поиска связанной области
function floodFillArea(startX: number, startY: number, width: number, height: number, paintedSet: Set<string>, visitedSet: Set<string>): Point[] {
  const area: Point[] = [];
  const queue: Point[] = [[startX, startY]];
  const localVisited = new Set<string>();
  
  while (queue.length > 0) {
    const [x, y] = queue.shift()!;
    const k = key(x, y);
    
    if (x < 0 || y < 0 || x >= width || y >= height) continue;
    if (paintedSet.has(k) || localVisited.has(k)) continue;
    
    localVisited.add(k);
    visitedSet.add(k);
    area.push([x, y]);
    
    // Останавливаемся если область слишком большая
    if (area.length > 400) break;
    
    // Добавляем соседей
    queue.push([x + 1, y], [x - 1, y], [x, y + 1], [x, y - 1]);
  }
  
  return area;
}

// Проверяем, достаточно ли область окружена TILES (мягкая проверка)
function isAreaMostlyEnclosedByTiles(area: Point[], tilesSet: Set<string>, width: number, height: number): boolean {
  let borderPoints = 0;
  let tilesNeighbors = 0;
  
  for (const [x, y] of area) {
    // Проверяем 8 соседей каждой точки области
    const neighbors = [
      [x + 1, y], [x - 1, y], [x, y + 1], [x, y - 1],
      [x + 1, y + 1], [x - 1, y - 1], [x + 1, y - 1], [x - 1, y + 1]
    ];
    
    let isOnBorder = false;
    for (const [nx, ny] of neighbors) {
      if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
      
      const nk = key(nx, ny);
      // Если сосед не в области - это граничная точка
      if (!area.some(([ax, ay]) => ax === nx && ay === ny)) {
        isOnBorder = true;
        if (tilesSet.has(nk)) {
          tilesNeighbors++;
        }
      }
    }
    
    if (isOnBorder) {
      borderPoints++;
    }
  }
  
  // Мягкое условие: хотя бы 60% граничных соседей должны быть TILES
  const tilesRatio = borderPoints > 0 ? tilesNeighbors / (borderPoints * 8) : 0;
  return tilesRatio >= 0.6;
}

// Находим подходящие места для размещения машин с проверкой стен
function findCarSpotsInArea(area: Point[], wallSet: Set<string>): Point[] {
  const spots: Point[] = [];
  const areaSet = new Set(area.map(([x, y]) => key(x, y)));
  
  for (const [x, y] of area) {
    // Проверяем размещение машины 2x5 (стандартный размер)
    const fits = checkCarFitsInArea(x, y, 2, 5, areaSet, wallSet);
    
    if (fits) {
      spots.push([x, y]);
    }
  }
  
  return spots;
}

// Проверяем, поместится ли машина с отступом от стен (для областей)
function checkCarFitsInArea(x: number, y: number, carWidth: number, carHeight: number, areaSet: Set<string>, wallSet: Set<string>): boolean {
  // Проверяем, что все клетки машины в области
  for (let dx = 0; dx < carWidth; dx++) {
    for (let dy = 0; dy < carHeight; dy++) {
      if (!areaSet.has(key(x + dx, y + dy))) {
        return false;
      }
    }
  }
  
  // Проверяем отступ от стен (минимум 2 клетки)
  for (let dx = -2; dx <= carWidth + 1; dx++) {
    for (let dy = -2; dy <= carHeight + 1; dy++) {
      const checkX = x + dx;
      const checkY = y + dy;
      if (wallSet.has(key(checkX, checkY))) {
        return false; // Слишком близко к стене
      }
    }
  }
  
  return true;
}

// Размещаем машины в области с правильным центрированием
function placeCarsInArea(location: TransferLocation, spots: Point[], wallSet: Set<string>, width: number, height: number): void {
  if (!location.decorations) location.decorations = {};
  if (!location.decorations[LocationDecorations.CAR]) {
    location.decorations[LocationDecorations.CAR] = [];
  }
  
  const placedCars: { x: number; y: number; width: number; height: number }[] = [];
  let carsPlaced = 0;
  const maxCars = 3; // максимум машин в области
  
  // Перемешиваем spots для случайности
  const shuffledSpots = [...spots].sort(() => Math.random() - 0.5);
  
  for (const [x, y] of shuffledSpots) {
    if (carsPlaced >= maxCars) break;
    
    // Проверяем отступ от уже размещенных машин (минимум 2 тайла)
    let tooClose = false;
    for (const car of placedCars) {
      const dist = Math.abs(x - car.x) + Math.abs(y - car.y);
      if (dist < 4) { // 2 тайла отступ = 4 в Manhattan distance
        tooClose = true;
        break;
      }
    }
    
    if (tooClose) continue;
    
    // Машина всегда 2x5 (ширина x высота) с отступом 2 тайла от стен
    const carWidth = 2;
    const carHeight = 5;
    
    // Проверяем, поместится ли машина с отступом от стен (2 тайла)
    const fitsWithWallMargin = x + carWidth + 1 < width && y + carHeight + 1 < height && 
                              x > 1 && y > 1 && !hasWallNearby(x, y, carWidth, carHeight, wallSet);
    
    if (!fitsWithWallMargin) continue;
    
    // Проверяем границы карты
    if (x + carWidth > width || y + carHeight > height) continue;
    
    // Проверяем пересечение с существующими машинами (как в streetsGenerator)
    let intersects = false;
    const existingCars = location.decorations[LocationDecorations.CAR] || [];
    for (const [carX, carY] of existingCars) {
      if (x < carX + 2 && x + carWidth > carX && y < carY + 5 && y + carHeight > carY) {
        intersects = true;
        break;
      }
    }
    
    if (intersects) continue;
    
    // Размещаем машину (как в streetsGenerator - addCarMultiBlock)
    for (let dx = 0; dx < carWidth; dx++) {
      for (let dy = 0; dy < carHeight; dy++) {
        location.decorations[LocationDecorations.CAR].push([x + dx, y + dy]);
      }
    }
    
    placedCars.push({ x, y, width: carWidth, height: carHeight });
    carsPlaced++;
    
  }
}

// Проверяем близость к стенам (отступ 2 тайла)
function hasWallNearby(x: number, y: number, carWidth: number, carHeight: number, wallSet: Set<string>): boolean {
  // Проверяем отступ от стен (минимум 2 клетки)
  for (let dx = -2; dx <= carWidth + 1; dx++) {
    for (let dy = -2; dy <= carHeight + 1; dy++) {
      const checkX = x + dx;
      const checkY = y + dy;
      if (wallSet.has(key(checkX, checkY))) {
        return true; // Слишком близко к стене
      }
    }
  }
  
  return false;
}

export default setLocationFloor;
