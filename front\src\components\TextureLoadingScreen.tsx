import React, { useEffect, useState } from 'react';
import styles from './TextureLoadingScreen.module.css';

interface TextureLoadingScreenProps {
  onComplete: () => void;
}

const TextureLoadingScreen: React.FC<TextureLoadingScreenProps> = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [loaded, setLoaded] = useState(0);
  const [total, setTotal] = useState(0);
  // currentFile / isLoading пока не нужны – держим минимум состояния

  useEffect(() => {
    const loadTextures = async () => {
      try {
        // Динамический импорт чтобы избежать проблем с JSON
  const { preloadAllTextures, getTotalTextureCount, getTextureInfo } = await import('../game/rendering/textures/TexturePreloader');
        
        const textureInfo = getTextureInfo();
        const totalCount = getTotalTextureCount();
        setTotal(totalCount);
        
        // Версия для текущей вкладки (sessionStorage) – если совпадает, просто завершаем мгновенно
        const sessionKey = 'texturesPreloadedVersion';
        const currentVersion = textureInfo.generatedAt;
        const sessionVersion = sessionStorage.getItem(sessionKey);
        if (sessionVersion === currentVersion) {
          // Проверяем что действительно все текстуры в кэше
          try {
            const { isAllTexturesPreloaded } = await import('../game/rendering/textures/TexturePreloader');
            if (isAllTexturesPreloaded()) {
              setProgress(100);
              setLoaded(totalCount);
              setTimeout(() => { onComplete(); }, 50);
              return;
            }
          } catch {}
          // Версия совпала но кэш не полный -> продолжаем обычную загрузку
        }
        
        // Реальный прогресс: вызываем preloadAllTextures с callback
        await preloadAllTextures((loadedNow, totalNow, percentage) => {
          setLoaded(loadedNow);
          setTotal(totalNow);
          setProgress(percentage);
        });

        // Сохраняем версию только в sessionStorage (текущая вкладка)
        sessionStorage.setItem(sessionKey, currentVersion);
        // Финальный небольшой delay чтобы пользователь увидел 100%
        setTimeout(async () => {
          try {
            const { getTextureInfo } = await import('../game/rendering/textures/TexturePreloader');
            const info = getTextureInfo();
            // Мини-проверка: если по какой-то причине прогресс < 100, не завершаем
            if (info.totalFiles && loaded < info.totalFiles) return;
          } catch {}
          onComplete();
        }, 150);

      } catch (error) {
        setTimeout(() => {
          onComplete();
        }, 500);
      }
    };

    loadTextures();
  }, [onComplete]);

  return (
    <div className={styles.loadingScreen}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <h1>Nuclear Story</h1>
          <div className={styles.subtitle}>Загрузка мира...</div>
        </div>
        
        <div className={styles.progressContainer}>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progress}%` }}
            />
          </div>
          
          <div className={styles.progressText}>
            {progress}% ({loaded} / {total})
          </div>
        </div>
        
  {/* Можно добавить имя текущего файла если перейдем на подробный прогресс */}
        
        <div className={styles.loadingAnimation}>
          <div className={styles.spinner}></div>
        </div>
        
        <div className={styles.tips}>
          <p>💡 Совет: Исследуйте каждый уголок мира - вас ждут сюрпризы!</p>
        </div>
      </div>
    </div>
  );
};

export default TextureLoadingScreen;
