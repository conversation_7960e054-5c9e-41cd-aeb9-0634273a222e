import { PresetLocationMap } from "../presetType";

export const smallShopPresets: PresetLocationMap[] = [
  {
    name: 'smallShop_1',
    width: 11,
  height: 9,
  tokenMap:  [
    [11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
    [11, 46, 53, 53, 22, 72, 72, 44, 44, 999, 11],
    [12, 999, 999, 32, 999, 999, 999, 21, 999, 999, 12],
    [11, 52, 52, 67, 999, 999, 999, 32, 66, 32, 11],
    [11, 32, 21, 999, 999, 999, 32, 32, 66, 999, 11],
    [12, 32, 66, 66, 66, 66, 21, 32, 66, 32, 12],
    [11, 999, 999, 999, 999, 32, 999, 999, 999, 999, 11],
    [11, 11, 11, 11, 32, 999, 999, 11, 11, 11, 11],
    [999, 999, 999, 11, 11, 15, 11, 11, 999, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];