import { textureLoader } from './TextureLoader';
import texturePathsData from './texturesPaths.json';

interface TexturePathsData {
  generatedAt: string;
  totalFiles: number;
  paths: string[];
}

/**
 * Проверяет действительно ли все текстуры находятся в кэше (а не только версия совпала)
 * Используем точную проверку по списку путей.
 */
export function isAllTexturesPreloaded(): boolean {
  const data = texturePathsData as TexturePathsData;
  const { paths } = data;
  for (const p of paths) {
    const img = textureLoader.getTexture(p);
    if (!img || !img.complete || img.width <= 0) return false;
  }
  return true;
}

/**
 * Загружает все текстуры из JSON файла с отчетом о прогрессе
 */
export async function preloadAllTextures(
  onProgress?: (loaded: number, total: number, percentage: number, currentPath: string) => void
): Promise<void> {
  const data = texturePathsData as TexturePathsData;
  const { paths, totalFiles } = data;
  
  let loaded = 0;

  // Загружаем батчами по 20 файлов параллельно для скорости
  const batchSize = 20;
  const batches: string[][] = [];
  
  for (let i = 0; i < paths.length; i += batchSize) {
    batches.push(paths.slice(i, i + batchSize));
  }

  for (const batch of batches) {
    const promises = batch.map(async (path) => {
      try {
        await textureLoader.loadTexture(path);
      } catch (error) {
        // Игнорируем ошибки загрузки
      }
      
      loaded++;
      const percentage = Math.round((loaded / totalFiles) * 100);
      
      if (onProgress) {
        onProgress(loaded, totalFiles, percentage, path);
      }
    });
    
    await Promise.all(promises);
    
    // Небольшая пауза между батчами для обновления UI
    await new Promise(resolve => setTimeout(resolve, 10));
  }
}

/**
 * Быстрая параллельная загрузка всех текстур (без прогресса)
 */
export async function preloadAllTexturesFast(): Promise<void> {
  const data = texturePathsData as TexturePathsData;
  const { paths, totalFiles } = data;
  
  // Разбиваем на батчи по 30 файлов для оптимальной скорости
  const batchSize = 30;
  const batches: string[][] = [];
  
  for (let i = 0; i < paths.length; i += batchSize) {
    batches.push(paths.slice(i, i + batchSize));
  }
  
  // Загружаем батчи последовательно, но внутри батча - параллельно
  for (const batch of batches) {
    const promises = batch.map(path => 
      textureLoader.loadTexture(path).catch(() => {
        // Игнорируем ошибки
      })
    );
    
    await Promise.all(promises);
  }
}

/**
 * Получает общее количество текстур для загрузки
 */
export function getTotalTextureCount(): number {
  const data = texturePathsData as TexturePathsData;
  return data.totalFiles;
}

/**
 * Получает информацию о текстурах
 */
export function getTextureInfo(): TexturePathsData {
  return texturePathsData as TexturePathsData;
}
