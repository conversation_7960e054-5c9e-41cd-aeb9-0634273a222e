/**
 * Система отрисовки текстур декораций с поддержкой decorationSides
 */

import { LocationDecorations, MaterialTexture, LocationSubtype } from '../../../shared/enums';
import { isDaytime } from '../../utils/time/gameTime';
import { gameTimeManager } from '../../utils/time/gameTimeManager';
import { textureLoader } from '../textures/TextureLoader';
import { DECORATION_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';
import { getVariationsForFolder, getRandomDeterministicVariation } from '../textures/variationIndex';
import { TIME_PERIODS } from '../../utils/constants/timeLight';


// Множество путей с неудачными загрузками — чтобы не считать placeholder валидной текстурой для рисования
const failedSideTextures = new Set<string>();

// Типы декораций, к которым применима эта логика (удобно расширять)
export const SUPPORTED_SIDE_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.WALL,
  LocationDecorations.WINDOW,
  LocationDecorations.DOOR,
  LocationDecorations.MOUNTAINWALL,
  LocationDecorations.FENCE,
  LocationDecorations.PARTITION,
  LocationDecorations.JAILDOOR,
];

//----------------------------------------------------------------------------------------
// Новый массив для декораций с одиночными сторонами (постеры, картины и т.д.)
export const SUPPORTED_SINGLE_SIDE_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.POSTER,
  LocationDecorations.TV,
  LocationDecorations.FIREPLACE,
  LocationDecorations.VENDINGMACHINE,
  LocationDecorations.TERMINAL,
  LocationDecorations.TRASHCONTAINER,
  LocationDecorations.SAFE,
  LocationDecorations.FRIDGE,
  LocationDecorations.CONTAINER,
  LocationDecorations.OVEN,
  LocationDecorations.TOOLBOX,
  LocationDecorations.TOILET,
  LocationDecorations.SINK,
  LocationDecorations.BARSHALF,
  LocationDecorations.SHELF,
  LocationDecorations.FIRSTAID,
  LocationDecorations.CABINET,
  LocationDecorations.WEAPONRACK,
  LocationDecorations.LOCKER,
  LocationDecorations.CHAIR,
  LocationDecorations.SHOWER,
  LocationDecorations.MILITARYCONTAINER,

];



//----------------------------------------------------------------------------------------
// Массив для декораций, которые соединяются сами с собой (мебель и т.д.)
export const SUPPORTED_SELF_CONNECTING_DECORATIONS: LocationDecorations[] = [

  LocationDecorations.RACK,
  LocationDecorations.BOOKSHELF,
  LocationDecorations.STORESHELF,
  LocationDecorations.STAIRS,
  LocationDecorations.BIOMONITOR,
  LocationDecorations.WORKBENCH,  
  LocationDecorations.TABLE,
  LocationDecorations.SUBWAYTURNSTILE,
  LocationDecorations.BENCH,
  LocationDecorations.EXTERIORLIGHT,
  LocationDecorations.GASSTATIONPUMP,
  LocationDecorations.CAR,
  LocationDecorations.DOUBLEBED,
  LocationDecorations.PRODUCTIONMACHINE,
  LocationDecorations.BLACKBOARD,
  LocationDecorations.SEACONTAINER,
  LocationDecorations.BILLIARDTABLE,
  LocationDecorations.SOFA,
  LocationDecorations.BUNKBED,
  LocationDecorations.LIQUIDTANK,
  LocationDecorations.GARAGEDOOR,
  LocationDecorations.WARMACHINE,
  LocationDecorations.BAD,
  LocationDecorations.BARCOUNTER,
  LocationDecorations.JAILBARS,
  LocationDecorations.FONTAIN,
  LocationDecorations.CASHREGISTER,
  LocationDecorations.BADSIDETABLE,
  LocationDecorations.TRAIN,
  LocationDecorations.SURGICALTABLE,
  LocationDecorations.ARMCHAIR,
  LocationDecorations.MASSIVECOMPUTER,
  LocationDecorations.BATH,


];
// Мапа подтипов для самосоединяющихся декораций - если подтип есть в списке, используется папка подтипа
const SELF_CONNECTING_SUBTYPE_DECORATIONS: Partial<Record<LocationDecorations, LocationSubtype[]>> = {
  [LocationDecorations.BAD]: [LocationSubtype.HOSPITAL],
  [LocationDecorations.BENCH]: [LocationSubtype.SUBWAY, LocationSubtype.POLICE, ],
  [LocationDecorations.WARMACHINE]: [LocationSubtype.HOSPITAL],
  // Добавляйте новые декорации и подтипы по необходимости
};



//----------------------------------------------------------------------------------------
// Массив для декораций без сторон, которые используют простую структуру папок по подтипам
export const SUPPORTED_NO_SIDES_DECORATIONS: LocationDecorations[] = [
  
  LocationDecorations.INTERIORLIGHT,
  LocationDecorations.SCELETON,
  LocationDecorations.TRASHBIN,
  LocationDecorations.ROCKS,
  LocationDecorations.BOX,
  LocationDecorations.BARSTOOL,
  LocationDecorations.CARPET,
  LocationDecorations.TIRE,
  LocationDecorations.LITTER,
  LocationDecorations.PALLET,
  LocationDecorations.UNIVERSALRND,
  LocationDecorations.SIGN,
  LocationDecorations.PUDDLE,
  LocationDecorations.POCKERTABLE,
  LocationDecorations.MUD,
  LocationDecorations.DEFENSESYSTEM,
  LocationDecorations.LOG,
  LocationDecorations.TREE,
  LocationDecorations.WHITEBOARD,
  LocationDecorations.BUSH,
  LocationDecorations.GRASS, 
  LocationDecorations.MEDCINECART,
  LocationDecorations.WATER,
  LocationDecorations.ROAD,
  LocationDecorations.BARREL,
  LocationDecorations.STREETLIGHT,
  LocationDecorations.FURNITURE,
  LocationDecorations.MANHOLECOVER,

];

// (day/night texture insertion removed — handled externally when/if needed)


//----------------------------------------------------------------------------------------
// Список декораций, которые должны становиться полупрозрачными под персонажем
export const TRANSPARENT_EXCEPTIONS: LocationDecorations[] = [
    LocationDecorations.WALL,
    LocationDecorations.WINDOW,
    LocationDecorations.DOOR,
    LocationDecorations.POSTER,
    LocationDecorations.MOUNTAINWALL,
    LocationDecorations.FENCE,
    LocationDecorations.PARTITION,

];

// Декорации, для которых текстуры зависят от дня/ночи — вставляем подпапку /day/ или /night/ перед именем файла
export const DAY_NIGHT_TEXTURES: LocationDecorations[] = [
  LocationDecorations.TREE,
  LocationDecorations.EXTERIORLIGHT,
  LocationDecorations.BUSH,
];

// Мапа подтипов для декораций без сторон - если подтип есть в списке, используется папка подтипа, иначе корневая папка
const NO_SIDES_SUBTYPE_DECORATIONS: Partial<Record<LocationDecorations, LocationSubtype[]>> = {
  [LocationDecorations.INTERIORLIGHT]: [LocationSubtype.SUBWAY],
  
};

//----------------------------------------------------------------------------------------
// Подтипы локаций, которые используют уникальные папки вместо материалов
const UNIQUE_SUBTYPE_DECORATIONS: Record<LocationSubtype, LocationDecorations[]> = {
  [LocationSubtype.SUBWAY]: [LocationDecorations.WINDOW, LocationDecorations.CAR,],
  [LocationSubtype.SHOP]: [LocationDecorations.WINDOW],
  // Остальные подтипы используют стандартную логику с материалами
  [LocationSubtype.GASSTATION]: [LocationDecorations.WINDOW],
  [LocationSubtype.HOTEL]: [],
  [LocationSubtype.TOWN]: [],
  [LocationSubtype.VILLAGE]: [],
  [LocationSubtype.CAMP]: [],
  [LocationSubtype.FARM]: [],
  [LocationSubtype.OTHER]: [],
  [LocationSubtype.MILITARY]: [LocationDecorations.FENCE],
  [LocationSubtype.BUNKER]: [],
  [LocationSubtype.SCHOOL]: [],
  [LocationSubtype.HOSPITAL]: [LocationDecorations.BAD],
  [LocationSubtype.LABORATORY]: [],
  [LocationSubtype.FACTORY]: [],
  [LocationSubtype.POLICE]: [],
  

};

//----------------------------------------------------------------------------------------
// Декорации с состояниями open/closed (удобно расширять)
const OPEN_CLOSE_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.DOOR,
  LocationDecorations.JAILDOOR,
];

// Декорации без материалов в пути (для open/close декораций)
const NO_MATERIAL_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.JAILDOOR,
];

/**
 * Проверяет, поддерживает ли декорация систему decorationSides
 */
export function isSideDecorationSupported(decoration: LocationDecorations): boolean {
  return SUPPORTED_SIDE_DECORATIONS.includes(decoration) || 
         SUPPORTED_SINGLE_SIDE_DECORATIONS.includes(decoration) ||
         SUPPORTED_SELF_CONNECTING_DECORATIONS.includes(decoration) ||
         SUPPORTED_NO_SIDES_DECORATIONS.includes(decoration);
}

// Удалены динамические индексации — теперь вариации читаются из заранее сгенерированного JSON

/**
 * Проверяет, нужно ли использовать уникальную папку подтипа вместо материала
 */
function shouldUseUniqueSubtype(decoration: LocationDecorations, locationSubtype?: LocationSubtype): boolean {
  if (!locationSubtype) return false;

  const uniqueDecorations = UNIQUE_SUBTYPE_DECORATIONS[locationSubtype];
  return uniqueDecorations && uniqueDecorations.includes(decoration);
}

/**
 * Общий fallback для декораций без сторон - использует логику как SUPPORTED_NO_SIDES_DECORATIONS
 */
function getNoSidesFallbackPaths(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  prefDayNight: (folder: string) => string,
  applyDayNightIfNeeded: (paths: string[], deco: LocationDecorations) => string[]
): string[] {
  // Для декораций без сторон координаты могут быть в decorationSides или отсутствовать
  const isoX = (decorationSides && decorationSides[0] !== undefined) ? Number(decorationSides[0]) : 0;
  const isoY = (decorationSides && decorationSides[1] !== undefined) ? Number(decorationSides[1]) : 0;

  // Получаем подтип локации
  const rawLocationSubtype = locationData?.subtype;
  const locationSubtype = typeof rawLocationSubtype === 'string'
    ? (rawLocationSubtype.toLowerCase() as LocationSubtype)
    : (rawLocationSubtype as LocationSubtype | undefined);

  const paths: string[] = [];

  // Проверяем, есть ли этот подтип в списке для данной декорации
  const supportedSubtypes = NO_SIDES_SUBTYPE_DECORATIONS[decoration];
  const useSubtypeFolder = locationSubtype && supportedSubtypes && supportedSubtypes.includes(locationSubtype);

  if (useSubtypeFolder && locationSubtype) {
    // Используем папку подтипа: /textures/Location/decorations/decoration/subtype/1.png
    const subtypeFolderPath = `/textures/Location/decorations/${decoration}/${locationSubtype}/`;
    const baseVariation = chooseDeterministicVariation(isoX, isoY, 'nosides', prefDayNight(subtypeFolderPath));
    const basePath = `${subtypeFolderPath}${baseVariation}.png`;
    paths.push(basePath);
  }

  // Fallback на корневую папку: /textures/Location/decorations/decoration/1.png
  const rootFolderPath = `/textures/Location/decorations/${decoration}/`;
  const baseRootVariation = chooseDeterministicVariation(isoX, isoY, 'nosides', prefDayNight(rootFolderPath));
  const baseRootPath = `${rootFolderPath}${baseRootVariation}.png`;
  paths.push(baseRootPath);

  return applyDayNightIfNeeded(paths, decoration);
}

/**
 * Загружает текстуру стороны декорации и кэширует её
 */
async function loadSideTexture(src: string): Promise<HTMLImageElement> {
  // Делегируем загрузку/кеширование центральному textureLoader.
  // textureLoader уже содержит механизмы де-дупликации загрузок и failedTextures.
  return textureLoader.loadTexture(src);
}

/**
 * Получает пути к текстурам для decorationSides
 */
export function getSideTexturePaths(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): string[] {
  // Проверяем поддержку обычных декораций
  const isSupported = SUPPORTED_SIDE_DECORATIONS.includes(decoration);
  const isSingleSideSupported = SUPPORTED_SINGLE_SIDE_DECORATIONS.includes(decoration);
  const isSelfConnectingSupported = SUPPORTED_SELF_CONNECTING_DECORATIONS.includes(decoration);
  const isNoSidesSupported = SUPPORTED_NO_SIDES_DECORATIONS.includes(decoration);
  
  if (!isSupported && !isSingleSideSupported && !isSelfConnectingSupported && !isNoSidesSupported) {
    return [];
  }

  // Вставляет /day/ или /night/ перед последним слэшем имени файла для путей, если декорация попадает в DAY_NIGHT_TEXTURES
  const applyDayNightIfNeeded = (inputPaths: string[], deco: LocationDecorations): string[] => {
    if (!DAY_NIGHT_TEXTURES.includes(deco)) return inputPaths;
    try {
      const hour = gameTimeManager.getCurrentTime().hour;
      const isDay = hour >= TIME_PERIODS.FULL_DAY.start && hour < TIME_PERIODS.FULL_DAY.end;
      return inputPaths.map(p => {
        const idx = p.lastIndexOf('/');
        if (idx === -1) return p;
        const dayNightFolder = `${p.slice(0, idx)}/${isDay ? 'day' : 'night'}/`;
        // Запускаем асинхронную индексацию для папки day/night — чтобы кэш заполнялся
  // Индексация больше не требуется
        return `${dayNightFolder}${p.slice(idx + 1)}`;
      });
    } catch (e) {
      return inputPaths;
    }
  };

  // Определяем день/ночь для этой функции один раз (используется для вычисления вариаций в папках day/night)
  const currentHour = gameTimeManager.getCurrentTime().hour;
  const isDayGlobal = currentHour >= TIME_PERIODS.FULL_DAY.start && currentHour < TIME_PERIODS.FULL_DAY.end;
  const prefDayNight = (folder: string) => {
    if (!DAY_NIGHT_TEXTURES.includes(decoration)) return folder;
    return `${folder}${isDayGlobal ? 'day/' : 'night/'}`;
  };

  // Для декораций без сторон - игнорируем decorationSides полностью
  if (isNoSidesSupported) {
    // Для декораций без сторон координаты могут быть в decorationSides или отсутствовать
    const isoX = (decorationSides && decorationSides[0] !== undefined) ? Number(decorationSides[0]) : 0;
    const isoY = (decorationSides && decorationSides[1] !== undefined) ? Number(decorationSides[1]) : 0;

    // Получаем подтип локации
    const rawLocationSubtype = locationData?.subtype;
    const locationSubtype = typeof rawLocationSubtype === 'string'
      ? (rawLocationSubtype.toLowerCase() as LocationSubtype)
      : (rawLocationSubtype as LocationSubtype | undefined);


    const paths: string[] = [];

    // Проверяем, есть ли этот подтип в списке для данной декорации
    const supportedSubtypes = NO_SIDES_SUBTYPE_DECORATIONS[decoration];
    const useSubtypeFolder = locationSubtype && supportedSubtypes && supportedSubtypes.includes(locationSubtype);


    if (useSubtypeFolder && locationSubtype) {
      // Используем папку подтипа: /textures/Location/decorations/interiorLight/subway/1.png
      const subtypeFolderPath = `/textures/Location/decorations/${decoration}/${locationSubtype}/`;
  const baseVariation = chooseDeterministicVariation(isoX, isoY, 'nosides', prefDayNight(subtypeFolderPath));
      const basePath = `${subtypeFolderPath}${baseVariation}.png`;

  // Добавляем базовый путь
  paths.push(basePath);
    }

    // Fallback на корневую папку: /textures/Location/decorations/interiorLight/1.png
    const rootFolderPath = `/textures/Location/decorations/${decoration}/`;
  const baseRootVariation = chooseDeterministicVariation(isoX, isoY, 'nosides', prefDayNight(rootFolderPath));
    const baseRootPath = `${rootFolderPath}${baseRootVariation}.png`;
  paths.push(baseRootPath);

  return applyDayNightIfNeeded(paths, decoration);
  }

  // Ожидаемый формат decorationSides: [x, y, number[]]
  if (!(decorationSides && decorationSides.length > 2 && Array.isArray(decorationSides[2]))) {
    // Fallback: если нет сторон, используем логику как для SUPPORTED_NO_SIDES_DECORATIONS
    return getNoSidesFallbackPaths(decoration, decorationSides, locationData, prefDayNight, applyDayNightIfNeeded);
  }

  const textureNumbers = decorationSides[2] as number[];
  if (!textureNumbers || textureNumbers.length === 0) {
    // Fallback: если массив сторон пустой, используем логику как для SUPPORTED_NO_SIDES_DECORATIONS
    return getNoSidesFallbackPaths(decoration, decorationSides, locationData, prefDayNight, applyDayNightIfNeeded);
  }

  // Выбор детерминированной вариации на основе координат
  const isoX = Number(decorationSides[0]) || 0;
  const isoY = Number(decorationSides[1]) || 0;

  const paths: string[] = [];

  // Логика для одиночных декораций (постеры и т.д.)
  if (isSingleSideSupported) {
    // Для одиночных декораций берем только первое направление, если есть
    const singleSide = textureNumbers.length > 0 ? textureNumbers[0] : 1; // fallback на направление 1
    const folderPath = `/textures/Location/decorations/${decoration}/${singleSide}/`;
  const variation = chooseDeterministicVariation(isoX, isoY, singleSide.toString(), prefDayNight(folderPath));

    // Простые пути без материалов и подтипов: /textures/Location/decorations/poster/1/1.png
    let singlePath = `/textures/Location/decorations/${decoration}/${singleSide}/${variation}.png`;
  paths.push(singlePath);

  return applyDayNightIfNeeded(paths, decoration);
  }

  // Логика для самосоединяющихся декораций (мебель и т.д.)
  if (isSelfConnectingSupported) {
    // Для самосоединяющихся декораций используем комбинации направлений как для стен
    const selfConnectingNumbers = textureNumbers.slice();
    
    // Сортируем номера направлений для стабильного имени папки
    let comboKey = selfConnectingNumbers.slice().sort((a, b) => a - b).join('_');
    if (selfConnectingNumbers.length === 1) {
      const only = selfConnectingNumbers[0];
      if (only === 1 || only === 3) comboKey = '1_3';
      else if (only === 2 || only === 4) comboKey = '2_4';
    }

    // Получаем подтип локации
    const rawLocationSubtype = locationData?.subtype;
    const locationSubtype = typeof rawLocationSubtype === 'string'
      ? (rawLocationSubtype.toLowerCase() as LocationSubtype)
      : (rawLocationSubtype as LocationSubtype | undefined);

    // Проверяем, есть ли этот подтип в списке для данной декорации
    const supportedSubtypes = SELF_CONNECTING_SUBTYPE_DECORATIONS[decoration];
    const useSubtypeFolder = locationSubtype && supportedSubtypes && supportedSubtypes.includes(locationSubtype);

    const paths: string[] = [];

    if (useSubtypeFolder && locationSubtype) {
      // Используем папку подтипа: /textures/Location/decorations/table/hospital/1_3/1.png
      const subtypeFolderPath = `/textures/Location/decorations/${decoration}/${locationSubtype}/${comboKey}/`;
  const subtypeVariation = chooseDeterministicVariation(isoX, isoY, comboKey, prefDayNight(subtypeFolderPath));
      paths.push(`${subtypeFolderPath}${subtypeVariation}.png`);
    }

    // Fallback на обычный путь: /textures/Location/decorations/table/1_3/1.png
    const folderPath = `/textures/Location/decorations/${decoration}/${comboKey}/`;
  const variation = chooseDeterministicVariation(isoX, isoY, comboKey, prefDayNight(folderPath));
    paths.push(`${folderPath}${variation}.png`);

  return applyDayNightIfNeeded(paths, decoration);
  }

  // Существующая логика для обычных декораций (стены, окна, двери)
  const textureNumbers2 = decorationSides[2] as number[];
  if (!textureNumbers2 || textureNumbers2.length === 0) {
    // Fallback: если массив сторон пустой, используем логику как для SUPPORTED_NO_SIDES_DECORATIONS
    return getNoSidesFallbackPaths(decoration, decorationSides, locationData, prefDayNight, applyDayNightIfNeeded);
  }

  // Сортируем номера направлений, чтобы получить стабильное имя папки, например "1_3_4"
  let comboKey = textureNumbers2.slice().sort((a, b) => a - b).join('_');
  if (textureNumbers2.length === 1) {
    const only = textureNumbers2[0];
    if (only === 1 || only === 3) comboKey = '1_3';
    else if (only === 2 || only === 4) comboKey = '2_4';
  }

  const textureMaterial = locationData?.textureMaterial ?? material ?? undefined;
  // Normalize subtype to string lowercase when possible — server/transfer data may provide different casing or formats
  const rawLocationSubtype = locationData?.subtype;
  const locationSubtype = typeof rawLocationSubtype === 'string'
    ? (rawLocationSubtype.toLowerCase() as LocationSubtype)
    : (rawLocationSubtype as LocationSubtype | undefined);

  // Проверяем, нужно ли использовать уникальную папку подтипа
  const useUniqueSubtype = shouldUseUniqueSubtype(decoration, locationSubtype);

  // Для декораций с состояниями open/closed поддерживаем отдельную папку открытого/закрытого состояния
  let openCloseFolder: string | undefined;
  if (OPEN_CLOSE_DECORATIONS.includes(decoration)) {
    // Пробуем найти флаг isOpen в разных местах структуры interactive
    const isOpen = Boolean(
      (locationData as any)?.interactive?.type?.isOpen ?? (locationData as any)?.interactive?.isOpen ?? false
    );
    openCloseFolder = isOpen ? 'open' : 'closed';
  }

  // Проверяем, нужно ли использовать материалы в пути
  const useMaterial = !NO_MATERIAL_DECORATIONS.includes(decoration);

  // Выбор детерминированной вариации на основе координат (чтобы при перерисовке не менялось)
  const isoX2 = Number(decorationSides[0]) || 0;
  const isoY2 = Number(decorationSides[1]) || 0;

  const paths2: string[] = [];

  // Если используем уникальный подтип, добавляем пути с папкой подтипа
  if (useUniqueSubtype && locationSubtype) {
    if (openCloseFolder) {
      // декорации с open/close: подтип/комбо/открытость/вариация
      const subtypeOpenFolderPath = `/textures/Location/decorations/${decoration}/${locationSubtype}/${comboKey}/${openCloseFolder}/`;
  const subtypeOpenVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(subtypeOpenFolderPath));
      paths2.push(`${subtypeOpenFolderPath}${subtypeOpenVariation}.png`);
    }
    // fallback без папки open/closed для подтипа
    const subtypeFolderPath = `/textures/Location/decorations/${decoration}/${locationSubtype}/${comboKey}/`;
  const subtypeVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(subtypeFolderPath));
    paths2.push(`${subtypeFolderPath}${subtypeVariation}.png`);
  }

  // Стандартная логика с материалом (только если декорация использует материалы)
  if (useMaterial && textureMaterial) {
    if (openCloseFolder) {
      // декорации с материалом: материал/комбо/открытость/вариация
      const materialOpenFolderPath = `/textures/Location/decorations/${decoration}/${textureMaterial}/${comboKey}/${openCloseFolder}/`;
  const materialOpenVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(materialOpenFolderPath));
      paths2.push(`${materialOpenFolderPath}${materialOpenVariation}.png`);
    }
    // fallback без папки open/closed
    const materialFolderPath = `/textures/Location/decorations/${decoration}/${textureMaterial}/${comboKey}/`;
  const materialVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(materialFolderPath));
    paths2.push(`${materialFolderPath}${materialVariation}.png`);
  }

  // Базовые пути без материала (всегда как fallback)
  if (openCloseFolder) {
    const baseFolderOpenPath = `/textures/Location/decorations/${decoration}/${comboKey}/${openCloseFolder}/`;
  const baseOpenVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(baseFolderOpenPath));
    paths2.push(`${baseFolderOpenPath}${baseOpenVariation}.png`);
  }
  const baseFolderPath = `/textures/Location/decorations/${decoration}/${comboKey}/`;
  const baseVariation = chooseDeterministicVariation(isoX2, isoY2, comboKey, prefDayNight(baseFolderPath));
  paths2.push(`${baseFolderPath}${baseVariation}.png`);

  return applyDayNightIfNeeded(paths2, decoration);
}

/**
 * Выбирает детерминированную вариацию по координатам и ключу комбинации
 */
function chooseDeterministicVariation(x: number, y: number, comboKey: string, folderPath: string): number {
  const variations = getVariationsForFolder(folderPath);
  if (!variations || variations.length === 0) return 1;
  return getRandomDeterministicVariation(folderPath, x, y, comboKey);
}

/**
 * Загружает все текстуры для decorationSides
 */
export async function loadSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): Promise<HTMLImageElement[]> {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, material);

  // Пробуем пути последовательно и возвращаем первую успешно загруженную текстуру
  for (const path of texturePaths) {
    try {
      // Проверяем централизованный кеш
      const cached = textureLoader.getTexture(path);
      if (cached && cached.complete && !textureLoader['failedTextures']?.has?.(path)) {
        return [cached];
      }

      const img = await loadSideTexture(path);
      // Проверяем, что это не placeholder и картинка валидна
      const isFailed = (textureLoader as any).failedTextures?.has?.(path) || failedSideTextures.has(path);
      if (!isFailed && img && img.complete && img.naturalWidth && img.naturalHeight) {
        return [img];
      }
      // иначе — пробуем следующий путь
    } catch (err) {
      // Пробуем следующий путь
    }
  }

  return [];
}

/**
 * Синхронно получает загруженные текстуры для decorationSides
 */
export function getLoadedSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): HTMLImageElement[] {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, material);

  // Возвращаем только загруженные (и полностью загруженные) изображения — порядок важен
  return texturePaths
    .map(path => ({ path, img: textureLoader.getTexture(path) }))
    .filter(entry => entry.img !== undefined && entry.img.complete && !failedSideTextures.has(entry.path))
    .map(entry => entry.img as HTMLImageElement);
}

/**
 * Отрисовывает текстуры decorationSides на тайле
 */
export function drawSideTextures(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture,
  playerPosition: { x: number; y: number } | null = null,
  tilePos: { x: number; y: number } | null = null
): void {
  // Мы ожидаем один итоговый файл для комбинации направлений — берём первый загруженный путь
  const loaded = getLoadedSideTextures(decoration, decorationSides, locationData, material);

  if (loaded.length === 0) {
    loadSideTextures(decoration, decorationSides, locationData, material);
    return;
  }

  const texture = loaded[0];
  if (!texture || !texture.complete) return;

  ctx.save();
  // Базовая alpha - больше НЕ затемняем декорации по darknessLevel (это делает отдельный слой)
  let alpha = 1;
  
  // НЕ применяем общее затемнение к декорациям
  // if (darknessLevel > 0) {
  //   alpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
  // }

  // Если есть позиция игрока и координаты тайла — применяем подсветку ТОЛЬКО для указанных относительных координат
  if (playerPosition && tilePos) {
    // Проверяем, не является ли декорация исключением из прозрачности
    const isTransparentException = TRANSPARENT_EXCEPTIONS.includes(decoration);
    
    if (isTransparentException) {
      const dx = tilePos.x - playerPosition.x;
      const dy = tilePos.y - playerPosition.y;
      // Разрешенные относительные позиции для подсветки: {-1,0}, {-1,1}, {0,1}
      const shouldHighlight = (dx === 1 && dy === 0) || (dx === 1 && dy === 1) || (dx === 0 && dy === 1) || (dx === 2 && dy === 1) || (dx === 2 && dy === 2) || (dx === 1 && dy === 2);
      if (shouldHighlight) {
        // Уменьшаем alpha до минимальной если нужно показать персонажа
        alpha = Math.min(alpha, 0.4); // MIN_ALPHA для видимости персонажа за препятствиями
      }
    }
  }

  ctx.globalAlpha = alpha;

  // Вычисляем размеры текстуры с учетом настроек из констант
  let drawW = texture.width;
  let drawH = texture.height;

  if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
    // Получаем ключ для декорации в верхнем регистре
    const decorationKey = decoration.toUpperCase();
    const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.getScale(decorationKey);

    drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

    if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        drawH = drawW / aspectRatio;
      } else {
        drawW = drawH * aspectRatio;
      }
    }
  }

  let offsetX = 0;
  let offsetY = 0;
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    const decorationKey = decoration.toUpperCase();
    offsetX = DECORATION_TEXTURE_SETTINGS.getHorizontalOffset(decorationKey);
    offsetY = DECORATION_TEXTURE_SETTINGS.getVerticalOffset(decorationKey);
  }

  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
  ctx.restore();
}

/**
 * Предзагружает текстуры для всех возможных комбинаций decorationSides
 */
export async function preloadSideTextures(): Promise<void> {
  // Опционально: можно пройти по всем вариациям из variationIndex и загрузить их, если нужно.
  // Пока оставляем пустую реализацию либо собираем минимальные пути при необходимости.
}

// day/night helper removed — day/night selection will be handled elsewhere if needed