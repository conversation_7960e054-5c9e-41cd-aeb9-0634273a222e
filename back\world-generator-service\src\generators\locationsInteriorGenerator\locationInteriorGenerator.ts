import { WorldMapCell } from '../../shared/types/World';
import { locationConfigs, LocationConfig } from './constants/locationConfig';
import { generateGridForLocation } from './generateGridForLocation';
import { placePresetsForLocation } from './placePresetForLocation';
import tokenLegend from './presets/tokenLegend';
import { generateRundimInterior } from './randomInteriorGenerator/mainInteriorGenerator';
import declareDecorationSides, { declareStairsOrientation } from './sideDeclarator';
import { runCleaningPasses } from './cleaningLocation';
import setLocationFloor from './setLocationFloor';
import { generateStreetsForLocation } from './streetsGenerator';
import { ProgressTracker, CancellationToken } from '../../utils/asyncUtils';

// tokenLegend values are numeric constants, allow number|string
const legend = tokenLegend as Record<string, string | number>;

// Основная асинхронная функция генерации контента для всех локаций
export async function generateLocationContentDataAsync(
	worldMap: { worldMapCells: WorldMapCell[] },
	rng: () => number,
	progressTracker?: ProgressTracker,
	cancellationToken?: CancellationToken
): Promise<void> {
	const cells = worldMap.worldMapCells.filter(cell => cell.location);
	const total = cells.length;

	if (total === 0) {
		if (progressTracker) {
			progressTracker.updateStageProgress(100, 'Локации не найдены');
		}
		return;
	}

	// Инициализируем прогресс
	if (progressTracker) {
		progressTracker.updateStageProgress(0, `Начало генерации интерьеров для ${total} локаций (randomInterior отключен)`);
	}

	// bounded concurrency worker-pool to avoid fully sequential processing
	let maxConcurrency = 4;
	try {
		const cpus = require('os').cpus?.().length;
		if (cpus && typeof cpus === 'number') maxConcurrency = Math.max(1, Math.floor(cpus / 2));
	} catch (e) {
		// fallback left as default
	}
	const CONCURRENCY = Math.min(Math.max(1, maxConcurrency), total, 8);

	let nextIndex = 0;
	let processed = 0;

	const workers: Promise<void>[] = [];
	for (let w = 0; w < CONCURRENCY; w++) {
		workers.push((async () => {
			while (true) {
				const i = nextIndex++;
				if (i >= total) break;
				if (cancellationToken?.isCancelled) throw new Error('Operation was cancelled');

				const cell = cells[i];
				const location = cell.location!;
				const subtype = location.subtype;
				const config = locationConfigs[subtype];

				await generateLocationInterior(cell, config, rng);

				// update progress (atomic-ish)
				processed++;
				if (progressTracker) {
					const progress = Math.floor((processed / total) * 100);
					progressTracker.updateStageProgress(progress, `Обработка интерьеров: ${processed}/${total} локаций (быстрый режим)`);
				}

				// cooperative yield to event loop occasionally (реже, так как randomInterior отключен)
				if (processed % 20 === 0) await new Promise(res => setImmediate(res));
			}
		})());
	}

	await Promise.all(workers);

	// Финальное обновление прогресса
	if (progressTracker) {
		progressTracker.updateStageProgress(100, `Обработка интерьеров завершена: ${total} локаций (randomInterior отключен)`);
	}
}

// Пошаговая генерация интерьера одной локации
async function generateLocationInterior(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number
): Promise<void> {
	// Шаг 1: Генерация грида и базового terrain
	await generateGridForLocation(cell, config, rng);

	// Шаг 2: Генерация улиц (ПЕРЕД buildingsPresetLogic)
	const streetAreas = await generateStreetsForLocation(cell.location!, config, rng);

	await generateRundimInterior(cell, config, rng);


	await placePresetsForLocation(cell, config, rng,
	 legend);

	// Run cleaning passes (e.g. outdoor tree spacing) before decorating sides
	await runCleaningPasses(cell.location!);

	await declareDecorationSides(cell.location!);
	await declareStairsOrientation(cell.location!);

	// post-process floors (paint floors per building/room/decorations)
	setLocationFloor(cell.location!);
}