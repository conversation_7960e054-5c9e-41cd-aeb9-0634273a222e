import { v4 as uuidv4 } from 'uuid';
import { WorldMap, WorldSettings, WorldMapCell, WorldWeatherState } from '../shared/types/World';
import { Player } from '../shared/models/Player';
import { Position } from '../shared/models/Player';
import { TerrainType, Language, WorldMapDecorations } from '../shared/enums';
import { CreateWorldDto } from '../dto/create-world.dto';
import { generateDecorationsAsync } from './utilsWorldMapGenerator/decorationGenerator';
import { cleanDecorationMapAsync } from './utilsWorldMapGenerator/decorationCleaner';
import { updateDecorationBordersAsync } from './utilsWorldMapGenerator/decorationBorderUpdater';
import { generateLocationsAsync } from './utilsWorldMapGenerator/locationWorldMapGeneratorAsync';
import { 
  GenerationProgress, 
  CancellationToken, 
  ProgressTracker
} from '../utils/asyncUtils';
import { TERRARIAN_MARKER_CONFIG } from './worldGeneratorConstants';
import { assignLocationSubtypesWithGuarantees } from './locationsInteriorGenerator/setLocationSubtype';
import { generateLocationContentDataAsync } from './locationsInteriorGenerator/locationInteriorGenerator';


// Оптимизированный асинхронный генератор основного объекта мира
export async function generateBaseWorldAsync(
  createWorldDto: CreateWorldDto,
  progressCallback?: (progress: GenerationProgress) => void,
  cancellationToken?: CancellationToken
): Promise<WorldMap> {
  const worldId = uuidv4();
  const currentDate = new Date();

  // Создаем трекер прогресса с правильным распределением весов
  const progressTracker = new ProgressTracker(progressCallback);
  progressTracker.addStage('Инициализация', 1);
  progressTracker.addStage('Создание базовой сетки', 3);  // Уменьшили с 6 до 3
  progressTracker.addStage('Генерация декораций', 2);
  progressTracker.addStage('Обновление границ декораций', 1);
  progressTracker.addStage('Создание локаций', 2);  // Увеличили с 1 до 2
  progressTracker.addStage('Очистка декораций конец', 1);
  progressTracker.addStage('Обновление границ дорог', 1);
  progressTracker.addStage('Генерация интерьеров локаций', 4);  // Увеличили с 1 до 4

  try {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    progressTracker.updateStageProgress(0, 'Создание базового объекта мира');

    // Генерация погоды и состояния мира
    const weatherState: WorldWeatherState = generateWorldWeatherState();

    // Генерация стартового игрока
    const worldSize = createWorldDto.settings.worldSize;
    const centerPos: Position = {
      x: Math.floor(worldSize / 2),
      y: Math.floor(worldSize / 2),
    };
    const initialPlayer: Player = generateInitialPlayer(createWorldDto.userId, createWorldDto.name || 'Player', centerPos);

    // Создание базового объекта мира
    const world: WorldMap = {
      id: worldId,
      userId: createWorldDto.userId,
      name: createWorldDto.name,
      description: createWorldDto.description,
      parameters: weatherState,
      player: initialPlayer,
      settings: {
        seed: createWorldDto.settings.seed,
        language: createWorldDto.settings.language as Language,
        worldSize: createWorldDto.settings.worldSize,
        difficulty: createWorldDto.settings.difficulty,
        autosave: createWorldDto.settings.autosave,
        timeScale: createWorldDto.settings.timeScale,
        showPath: true,
        volume: 1,
        backgroundMusic: 1,
        soundEffects: 1,
        brightness: 50,
        contrast: 50,
        terminalTextSize: 3,
        worldMapPlayerSpeed: 2,
      },
      worldMap: {},
      createdAt: currentDate,
      updatedAt: currentDate,
    };

    progressTracker.updateStageProgress(100, 'Базовый объект мира создан');
    progressTracker.nextStage('Начало генерации карты мира');

    // Оптимизированная асинхронная генерация карты мира
    world.worldMap = await generateWorldGridAsync(
      world.settings, 
      world.settings.seed, 
      progressTracker,
      cancellationToken
    );

    // Этап 9: Генерация интерьеров локаций
    progressTracker.nextStage('Генерация интерьеров локаций');
    progressTracker.updateStageProgress(0, 'Начало генерации интерьеров локаций');

    // Собираем все локации из карты мира
    const locations = Object.values(world.worldMap)
      .filter(cell => cell.location)
      .map(cell => cell.location!);

    // Генерируем интерьеры локаций
    if (locations.length > 0) {
      await generateLocationContentDataAsync(
        { worldMapCells: Object.values(world.worldMap) },
        createSeededRandom(world.settings.seed + '_interiors'), // Отдельный seed для интерьеров
        progressTracker,
        cancellationToken
      );
    } else {
      progressTracker.updateStageProgress(100, 'Локации не найдены, пропускаем генерацию интерьеров');
    }

    progressTracker.complete();
    
    // Логируем финальный размер созданного мира
    const worldCellsCount = Object.keys(world.worldMap).length;
    const locationsCount = Object.values(world.worldMap).filter(cell => cell.location).length;
    
    // Вычисляем размер мира в мегабайтах
    const worldSizeInBytes = JSON.stringify(world).length;
    const worldSizeInMB = (worldSizeInBytes / (1024 * 1024)).toFixed(2);
    
    console.log(`🌍 Мир создан успешно:`);
    console.log(`   📏 Размер карты: ${world.settings.worldSize}x${world.settings.worldSize}`);
    console.log(`   🔢 Всего клеток: ${worldCellsCount}`);
    console.log(`   🏠 Локаций создано: ${locationsCount}`);
    console.log(`   💾 Размер мира: ${worldSizeInMB} МБ`);
    console.log(`   🆔 ID мира: ${world.id}`);
    
    return world;

  } catch (error) {
    if (error.message === 'Operation was cancelled') {
      throw error;
    }
    throw new Error(`Ошибка генерации мира: ${error.message}`);
  }
}

// Генератор стартового игрока
function generateInitialPlayer(userId: string, name: string, position: Position): Player {
  const now = new Date();
  return {
    id: uuidv4(),
    name,
    level: 1,
    experience: 0,
    experienceToNext: 100,
    imgDirection: 1,
    parameters: { Might: 5, Perception: 5, Stamina: 5, Charm: 5, Intelligence: 5, Dexterity: 5, Fortune: 5 },
    currentHP: 100,
    maxHP: 100,
    currentAP: 50,
    maxAP: 50,
    radiationLevel: 0,
    hunger: 0,
    thirst: 0,
    fatigue: 0,
    perks: [],
    skills: {},
    inventory: [],
    position: position,
    effects: [],
    equippedArmor: '',
    completedQuests: [],
    activeQuests: [],
    discoveredLocations: [],
    factionReputation: {},
    createdAt: now,
    lastSaveAt: now,
  };
}

// Генератор состояния погоды и времени в мире
function generateWorldWeatherState(): WorldWeatherState {
  return {
    currentTime: {
      day: 1,
      hour: 12,
      minute: 0,
      season: 'spring',
    },
    weather: {
      temperature: 20,
      humidity: 65,
      windSpeed: 5,
      precipitation: 0,
      visibility: 100,
      radiationStorm: false,
    },
    activeEvents: [],
  };
}

// Алиас для обратной совместимости
export const generateWorldGrid = generateWorldGridAsync;

// Оптимизированный асинхронный генератор сетки карты мира
async function generateWorldGridAsync(
  settings: WorldSettings, 
  seed: string,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<Record<string, WorldMapCell>> {
  const grid: Record<string, WorldMapCell> = {};
  const worldSize = settings.worldSize;
  const rng = createSeededRandom(seed);

  // Этап 1: Создание базовых клеток (синхронно для максимальной скорости)
  progressTracker.updateStageProgress(0, 'Создание базовых клеток карты');

  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  const updateInterval = Math.max(1, Math.floor(worldSize / 10));

  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const pos: Position = { x, y };
      const cellKey = `${x},${y}`;

      let height = 1;
      if (x === 0 || y === 0 || x === worldSize - 1 || y === worldSize - 1) height = 0;
      else if ((x + y) % 7 === 0) height = 2;
      else if ((x * y) % 13 === 0) height = 3;
      else if ((x + y) % 17 === 0) height = 4;

      grid[cellKey] = {
        pos,
        blocked: false,
        terrarianMarker: 0,
        terrain: TerrainType.WASTELAND,
        height,
        location: undefined,
        decoration: WorldMapDecorations.NONE,
        fogOfWar: true,
        locationNear: false,
        imgDirection: 1,
        LVLZone: 1,
      };

      processedCells++;
    }

    // Проверяем отмену и обновляем прогресс реже
    if (x % updateInterval === 0) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      const progress = Math.floor((processedCells / totalCells) * 25); // 25% от этапа сетки
      progressTracker.updateStageProgress(progress, `Создание базовых клеток: ${processedCells}/${totalCells}`);
      await new Promise(resolve => setImmediate(resolve));
    }
  }

  // Открываем туман ойны вокруг центра
  const centerX = Math.floor(worldSize / 2);
  const centerY = Math.floor(worldSize / 2);
  openFogOfWar(grid, { x: centerX, y: centerY }, 7);

  // Этап 2: Террариан маркеры (синхронно)
  progressTracker.updateStageProgress(25, 'Размещение террариан маркеров');

  let markerCount = 0;
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      const hasMarker = rng() < TERRARIAN_MARKER_CONFIG.SPAWN_CHANCE ? 1 : 0;
      grid[cellKey].terrarianMarker = hasMarker;
      if (hasMarker) markerCount++;
    }

    if (x % updateInterval === 0) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      const progress = 25 + Math.floor((x / worldSize) * 25); // 25-50% от этапа сетки
      progressTracker.updateStageProgress(progress, `Террариан маркеры: ${x}/${worldSize} строк`);
      await new Promise(resolve => setImmediate(resolve));
    }
  }

  // Проверка на аномальное количество маркеров (пропорционально размеру карты)
  // Для карты 200x200 максимум 5000 маркеров, для остальных пропорционально
  const maxMarkersFor200x200 = 5000;
  const maxAllowedMarkers = Math.floor((worldSize * worldSize / (200 * 200)) * maxMarkersFor200x200);
  
  if (markerCount > maxAllowedMarkers) {
    const ironicMessages = [
      "В детстве вам говорили не ходить по тонкому льду, но вы всё равно пошли. Теперь лёд треснул, и генератор мира провалился в бездну багов.",
      "Боги генерации мира разгневались на ваши дерзкие попытки создать идеальный мир. Попробуйте ещё раз, смиренно.",
      "Алхимия создания миров - дело тонкое. Видимо, вы добавили слишком много серы в котёл. Мир взорвался сильнее обычного.",
      "Звёзды сошлись неудачно для генерации этого мира. Астрологи рекомендуют попробовать в другое время.",
      "Генератор мира споткнулся о собственную бороду и упал в яму с багами. Помогите ему встать, попробовав ещё раз.",
      "Этот мир оказался слишком амбициозным для нашего скромного генератора. Попробуйте что-то попроще.",
      "Кажется, драконы генерации мира сегодня не в настроении. Возвращайтесь завтра с подношениями в виде перезапуска."
    ];
    
    const randomMessage = ironicMessages[Math.floor(Math.random() * ironicMessages.length)];
    throw new Error(`GENERATION_ANOMALY: ${randomMessage} (Маркеров: ${markerCount}, максимум: ${maxAllowedMarkers} для карты ${worldSize}x${worldSize})`);
  }

  // Этап 3: Зоны уровней (синхронно)
  progressTracker.updateStageProgress(50, 'Расчет зон уровней');

  const center = worldSize / 2;
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      const dist = Math.sqrt((x - center) ** 2 + (y - center) ** 2);
      let LVLZone: 1 | 2 | 3 | 4 = 1;
      if (dist > worldSize * 0.4) LVLZone = 4;
      else if (dist > worldSize * 0.27) LVLZone = 3;
      else if (dist > worldSize * 0.13) LVLZone = 2;
      grid[cellKey].LVLZone = LVLZone;
    }

    if (x % updateInterval === 0) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      const progress = 50 + Math.floor((x / worldSize) * 25); // 50-75% от этапа сетки
      progressTracker.updateStageProgress(progress, `Зоны уровней: ${x}/${worldSize} строк`);
      await new Promise(resolve => setImmediate(resolve));
    }
  }

  // Этап 4: Направления изображений (синхронно)
  progressTracker.updateStageProgress(75, 'Установка направлений изображений');

  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      const imgDirection = (Math.floor(rng() * 4) + 1) as 1 | 2 | 3 | 4;
      grid[cellKey].imgDirection = imgDirection;
    }

    if (x % updateInterval === 0) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      const progress = 75 + Math.floor((x / worldSize) * 25); // 75-100% от этапа сетки
      progressTracker.updateStageProgress(progress, `Направления: ${x}/${worldSize} строк`);
      await new Promise(resolve => setImmediate(resolve));
    }
  }

  // Завершаем этап создания базовой сетки
  progressTracker.updateStageProgress(100, 'Базовая сетка создана');

  // Этап 5: Генерация декораций (асинхронно)
  progressTracker.nextStage('Генерация декораций');
  
  await generateDecorationsAsync(grid, worldSize, rng, progressTracker, cancellationToken);

  // Этап 6: Очистка декораций (асинхронно)
  progressTracker.nextStage('Очистка декораций');
  progressTracker.updateStageProgress(0, 'Начало очистки изолированных декораций');


  // Этап 7: Обновление границ декораций (асинхронно)
  progressTracker.nextStage('Обновление границ декораций');
  progressTracker.updateStageProgress(0, 'Начало обновления границ декораций');

  progressTracker.nextStage('Создание локаций');
  progressTracker.updateStageProgress(0, 'Начало создания локаций');
  
  await generateLocationsAsync(grid, worldSize, rng, 0.02, progressTracker, cancellationToken);


  await assignLocationSubtypesWithGuarantees({ worldMapCells: Object.values(grid) }, rng);

  progressTracker.updateStageProgress(100, 'Локации созданы');


  
  // Этап 8: Обновление границ дорог (асинхронно)


  progressTracker.nextStage('Обновление границ дорог');
  progressTracker.updateStageProgress(0, 'Начало обновления границ дорог');

    
  await cleanDecorationMapAsync(grid, worldSize, rng, progressTracker, cancellationToken);
  progressTracker.updateStageProgress(100, 'Очистка декораций завершена');


  await updateDecorationBordersAsync(grid, worldSize, progressTracker, cancellationToken);
  progressTracker.updateStageProgress(100, 'Границы дорог обновлены');
  return grid;
}

// Открывает туман войны в радиусе radius от позиции pos
function openFogOfWar(grid: Record<string, WorldMapCell>, pos: Position, radius: number) {
  for (let x = pos.x - radius; x <= pos.x + radius; x++) {
    for (let y = pos.y - radius; y <= pos.y + radius; y++) {
      const dx = x - pos.x;
      const dy = y - pos.y;
      if (dx * dx + dy * dy <= radius * radius) {
        const cellKey = `${x},${y}`;
        if (grid[cellKey]) {
          grid[cellKey].fogOfWar = false;
        }
      }
    }
  }
}

// Создает псевдо-случайный генератор чисел на основе сида
function createSeededRandom(seed: string) {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = (hash << 5) - hash + seed.charCodeAt(i);
    hash |= 0;
  }

  return function () {
    hash = (hash * 9301 + 49297) % 233280;
    return hash / 233280;
  };
}