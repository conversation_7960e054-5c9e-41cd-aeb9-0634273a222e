class TextureLoader {
  // Кеширование текстур для оптимизации производительности
  private textureCache: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();
  // Пути, у которых загрузка не удалась и мы сохранили placeholder — чтобы не использовать их для рисования
  private failedTextures: Set<string> = new Set();

  async loadTexture(path: string): Promise<HTMLImageElement> {
    // Проверяем кеш
    if (this.textureCache.has(path)) {
      return this.textureCache.get(path)!;
    }

    // Проверяем, не загружается ли уже эта текстура
    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path)!;
    }

    // Создаем новый промис загрузки
    const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.onload = async () => {
        try {
          // Гарантируем декодирование если поддерживается
          if (img.decode) {
            try {
              await img.decode();
            } catch (_) {
              // ignore decode errors, already loaded
            }
          }
          this.textureCache.set(path, img);
          this.loadingPromises.delete(path);
          resolve(img);
        } catch (e) {
          this.loadingPromises.delete(path);
          resolve(img); // даже если decode сломался, изображение загружено
        }
      };
      img.onerror = () => {
        // Вместо постоянного спама ошибок — создаём небольшой прозрачный placeholder и кешируем его,
        // чтобы дальнейшие запросы не пытались загружать заново и не генерировали ошибки.
        this.loadingPromises.delete(path);
        try {
          const canvas = document.createElement('canvas');
          canvas.width = 1;
          canvas.height = 1;
          const ctx = canvas.getContext('2d');
          if (ctx) ctx.clearRect(0, 0, 1, 1);
          const data = canvas.toDataURL();
          const placeholder = new Image();
          placeholder.onload = () => {
            this.textureCache.set(path, placeholder);
            // Отмечаем как неудачную загрузку
            this.failedTextures.add(path);
            resolve(placeholder);
          };
          // Если по каким-то причинам создание placeholder не сработает, всё равно отклоняем
          placeholder.onerror = () => reject(new Error(`Failed to create placeholder for: ${path}`));
          placeholder.src = data;
        } catch (e) {
          reject(new Error(`Failed to load texture and create placeholder: ${path}`));
        }
      };
      img.src = path;
    });

    // Сохраняем промис загрузки
    this.loadingPromises.set(path, loadPromise);
    return loadPromise;
  }

  async preloadTextures(textures: Record<string, string[]>): Promise<void> {
    const allPromises: Promise<HTMLImageElement>[] = [];
    for (const textureList of Object.values(textures)) {
      for (const texturePath of textureList) {
        allPromises.push(this.loadTexture(texturePath));
      }
    }
    await Promise.all(allPromises);
  }

  getTexture(path: string): HTMLImageElement | undefined {
    // Возвращаем текстуру из кеша
  if (this.failedTextures.has(path)) return undefined;
  return this.textureCache.get(path);
  }

  /**
   * Возвращает статистику загрузки текстур
   */
  getStats() {
    return {
      cached: this.textureCache.size,
      loading: this.loadingPromises.size,
      failed: this.failedTextures.size
    };
  }

  /**
   * Очищает кеш (осторожно!)
   */
  clearCache() {
    this.textureCache.clear();
    this.loadingPromises.clear();
    this.failedTextures.clear();
  }
}

export const textureLoader = new TextureLoader();