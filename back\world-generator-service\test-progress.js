/**
 * Тестовый скрипт для проверки системы прогресса генерации мира
 */

const { ProgressTracker } = require('./dist/utils/asyncUtils');

// Симуляция весов этапов из worldGenerator.ts
function testProgressDistribution() {
  console.log('🧪 Тестирование распределения прогресса...\n');

  const progressLogs = [];
  
  const progressTracker = new ProgressTracker((progress) => {
    const logEntry = `${progress.progress}% - ${progress.stage}: ${progress.currentOperation}`;
    progressLogs.push(logEntry);
    console.log(logEntry);
  });

  // Добавляем этапы с весами для отключенного randomInterior
  progressTracker.addStage('Инициализация', 1);
  progressTracker.addStage('Создание базовой сетки', 4);  // Увеличили обратно
  progressTracker.addStage('Генерация декораций', 3);  // Увеличили
  progressTracker.addStage('Обновление границ декораций', 1);
  progressTracker.addStage('Создание локаций', 3);  // Увеличили
  progressTracker.addStage('Очистка декораций конец', 1);
  progressTracker.addStage('Обновление границ дорог', 1);
  progressTracker.addStage('Генерация интерьеров локаций', 1);  // Уменьшили, так как отключен

  // Симулируем выполнение этапов
  console.log('\n📊 Симуляция выполнения этапов:\n');

  // Этап 1: Инициализация
  progressTracker.updateStageProgress(0, 'Создание базового объекта мира');
  progressTracker.updateStageProgress(100, 'Базовый объект мира создан');
  progressTracker.nextStage('Начало генерации карты мира');

  // Этап 2: Создание базовой сетки (симулируем подэтапы)
  progressTracker.updateStageProgress(0, 'Создание базовых клеток карты');
  progressTracker.updateStageProgress(25, 'Размещение террариан маркеров');
  progressTracker.updateStageProgress(50, 'Расчет зон уровней');
  progressTracker.updateStageProgress(75, 'Установка направлений изображений');
  progressTracker.updateStageProgress(100, 'Базовая сетка создана');
  progressTracker.nextStage('Генерация декораций');

  // Этап 3: Генерация декораций
  progressTracker.updateStageProgress(0, 'Стабильная генерация декораций');
  progressTracker.updateStageProgress(50, 'Генерация шаблонных декораций');
  progressTracker.updateStageProgress(100, 'Все декорации сгенерированы');
  progressTracker.nextStage('Обновление границ декораций');

  // Этап 4: Обновление границ декораций
  progressTracker.updateStageProgress(0, 'Начало обновления границ декораций');
  progressTracker.updateStageProgress(100, 'Границы декораций обновлены');
  progressTracker.nextStage('Создание локаций');

  // Этап 5: Создание локаций
  progressTracker.updateStageProgress(0, 'Разметка границ карты');
  progressTracker.updateStageProgress(20, 'Генерация основных локаций');
  progressTracker.updateStageProgress(60, 'Генерация городов и руин');
  progressTracker.updateStageProgress(80, 'Генерация дорог');
  progressTracker.updateStageProgress(100, 'Локации созданы');
  progressTracker.nextStage('Очистка декораций конец');

  // Этап 6: Очистка декораций конец
  progressTracker.updateStageProgress(0, 'Начало очистки декораций');
  progressTracker.updateStageProgress(100, 'Очистка декораций завершена');
  progressTracker.nextStage('Обновление границ дорог');

  // Этап 7: Обновление границ дорог
  progressTracker.updateStageProgress(0, 'Начало обновления границ дорог');
  progressTracker.updateStageProgress(100, 'Границы дорог обновлены');
  progressTracker.nextStage('Генерация интерьеров локаций');

  // Этап 8: Генерация интерьеров локаций (быстрый, так как randomInterior отключен)
  progressTracker.updateStageProgress(0, 'Начало генерации интерьеров для 50 локаций (randomInterior отключен)');
  progressTracker.updateStageProgress(50, 'Обработка интерьеров: 25/50 локаций (быстрый режим)');
  progressTracker.updateStageProgress(100, 'Обработка интерьеров завершена: 50 локаций (randomInterior отключен)');

  progressTracker.complete();

  console.log('\n📈 Анализ распределения прогресса:');
  
  // Анализируем ключевые точки прогресса
  const keyPoints = progressLogs.filter(log => {
    const progress = parseInt(log.split('%')[0]);
    return [10, 20, 30, 40, 50, 60, 70, 80, 90, 100].includes(progress);
  });

  console.log('\n🎯 Ключевые точки прогресса:');
  keyPoints.forEach(point => console.log(`  ${point}`));

  console.log('\n✅ Тест завершен!');
}

// Запускаем тест
testProgressDistribution();
